package com.sinon.quizplatform.exception;

/**
 * 未授权异常（权限不足）
 */
public class AuthorizationException extends BusinessException {
    
    public AuthorizationException() {
        super(403, "权限不足");
    }
    
    public AuthorizationException(String message) {
        super(403, message);
    }
    
    public AuthorizationException(String message, Throwable cause) {
        super(403, message, cause);
    }
}
