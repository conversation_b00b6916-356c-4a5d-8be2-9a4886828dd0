package com.sinon.quizplatform.util;

import com.sinon.quizplatform.exception.ValidationException;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 校验工具类
 */
public class ValidationUtil {
    
    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    /**
     * 手机号正则表达式
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile(
            "^1[3-9]\\d{9}$"
    );
    
    /**
     * 用户名正则表达式（4-20位字母数字下划线）
     */
    private static final Pattern USERNAME_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_]{4,20}$"
    );
    
    /**
     * 校验字符串不为空
     * @param value 待校验值
     * @param fieldName 字段名
     * @throws ValidationException 校验异常
     */
    public static void requireNonEmpty(String value, String fieldName) {
        if (!StringUtils.hasText(value)) {
            throw new ValidationException(fieldName + "不能为空");
        }
    }
    
    /**
     * 校验对象不为空
     * @param value 待校验值
     * @param fieldName 字段名
     * @throws ValidationException 校验异常
     */
    public static void requireNonNull(Object value, String fieldName) {
        if (value == null) {
            throw new ValidationException(fieldName + "不能为空");
        }
    }
    
    /**
     * 校验邮箱格式
     * @param email 邮箱
     * @throws ValidationException 校验异常
     */
    public static void validateEmail(String email) {
        requireNonEmpty(email, "邮箱");
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw new ValidationException("邮箱格式不正确");
        }
    }
    
    /**
     * 校验手机号格式
     * @param phone 手机号
     * @throws ValidationException 校验异常
     */
    public static void validatePhone(String phone) {
        requireNonEmpty(phone, "手机号");
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            throw new ValidationException("手机号格式不正确");
        }
    }
    
    /**
     * 校验用户名格式
     * @param username 用户名
     * @throws ValidationException 校验异常
     */
    public static void validateUsername(String username) {
        requireNonEmpty(username, "用户名");
        if (!USERNAME_PATTERN.matcher(username).matches()) {
            throw new ValidationException("用户名格式不正确，应为4-20位字母、数字或下划线");
        }
    }
    
    /**
     * 校验密码强度
     * @param password 密码
     * @throws ValidationException 校验异常
     */
    public static void validatePassword(String password) {
        requireNonEmpty(password, "密码");
        if (password.length() < 6 || password.length() > 20) {
            throw new ValidationException("密码长度应为6-20位");
        }
    }
    
    /**
     * 校验字符串长度
     * @param value 待校验值
     * @param fieldName 字段名
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @throws ValidationException 校验异常
     */
    public static void validateLength(String value, String fieldName, int minLength, int maxLength) {
        requireNonEmpty(value, fieldName);
        if (value.length() < minLength || value.length() > maxLength) {
            throw new ValidationException(fieldName + "长度应为" + minLength + "-" + maxLength + "位");
        }
    }
    
    /**
     * 校验数值范围
     * @param value 待校验值
     * @param fieldName 字段名
     * @param min 最小值
     * @param max 最大值
     * @throws ValidationException 校验异常
     */
    public static void validateRange(Integer value, String fieldName, int min, int max) {
        requireNonNull(value, fieldName);
        if (value < min || value > max) {
            throw new ValidationException(fieldName + "应在" + min + "-" + max + "之间");
        }
    }
}
