package com.sinon.quizplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户在各科目的统计实体类
 * 对应数据库表：user_subject_progress
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Entity
@Data
@Table(name = "user_subject_progress")
public class UserSubjectProgress {
    
    /**
     * 记录ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 关联的用户实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    /**
     * 科目ID
     */
    @Column(name = "subject_id", nullable = false)
    @NotNull(message = "科目ID不能为空")
    private Integer subjectId;
    
    /**
     * 关联的科目实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subject_id", insertable = false, updatable = false)
    private Subject subject;
    
    /**
     * 累计作答次数
     */
    @Column(name = "total_attempt", nullable = false)
    @NotNull(message = "累计作答次数不能为空")
    @Min(value = 0, message = "累计作答次数不能小于0")
    private Integer totalAttempt = 0;
    
    /**
     * 累计正确次数
     */
    @Column(name = "correct_count", nullable = false)
    @NotNull(message = "累计正确次数不能为空")
    @Min(value = 0, message = "累计正确次数不能小于0")
    private Integer correctCount = 0;
    
    /**
     * 最近一次提交时间
     */
    @Column(name = "last_submit")
    private LocalDateTime lastSubmit;
    
    /**
     * 默认构造函数
     */
    public UserSubjectProgress() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param subjectId 科目ID
     */
    public UserSubjectProgress(Long userId, Integer subjectId) {
        this.userId = userId;
        this.subjectId = subjectId;
        this.totalAttempt = 0;
        this.correctCount = 0;
    }
    
    /**
     * 增加作答次数
     * 
     * @param isCorrect 是否正确
     */
    public void incrementAttempt(boolean isCorrect) {
        this.totalAttempt++;
        if (isCorrect) {
            this.correctCount++;
        }
        this.lastSubmit = LocalDateTime.now();
    }
    
    /**
     * 计算正确率
     * 
     * @return 正确率（0.0-1.0）
     */
    public double getAccuracyRate() {
        if (totalAttempt == 0) {
            return 0.0;
        }
        return (double) correctCount / totalAttempt;
    }
    
    // Getters and Setters
    
    /**
     * 获取记录ID
     * 
     * @return 记录ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 设置记录ID
     * 
     * @param id 记录ID
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public Long getUserId() {
        return userId;
    }
    
    /**
     * 设置用户ID
     * 
     * @param userId 用户ID
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    /**
     * 获取关联的用户实体
     * 
     * @return 用户实体
     */
    public User getUser() {
        return user;
    }
    
    /**
     * 设置关联的用户实体
     * 
     * @param user 用户实体
     */
    public void setUser(User user) {
        this.user = user;
    }
    
    /**
     * 获取科目ID
     * 
     * @return 科目ID
     */
    public Integer getSubjectId() {
        return subjectId;
    }
    
    /**
     * 设置科目ID
     * 
     * @param subjectId 科目ID
     */
    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }
    
    /**
     * 获取关联的科目实体
     * 
     * @return 科目实体
     */
    public Subject getSubject() {
        return subject;
    }
    
    /**
     * 设置关联的科目实体
     * 
     * @param subject 科目实体
     */
    public void setSubject(Subject subject) {
        this.subject = subject;
    }
    
    /**
     * 获取累计作答次数
     * 
     * @return 累计作答次数
     */
    public Integer getTotalAttempt() {
        return totalAttempt;
    }
    
    /**
     * 设置累计作答次数
     * 
     * @param totalAttempt 累计作答次数
     */
    public void setTotalAttempt(Integer totalAttempt) {
        this.totalAttempt = totalAttempt;
    }
    
    /**
     * 获取累计正确次数
     * 
     * @return 累计正确次数
     */
    public Integer getCorrectCount() {
        return correctCount;
    }
    
    /**
     * 设置累计正确次数
     * 
     * @param correctCount 累计正确次数
     */
    public void setCorrectCount(Integer correctCount) {
        this.correctCount = correctCount;
    }
    
    /**
     * 获取最近一次提交时间
     * 
     * @return 最近一次提交时间
     */
    public LocalDateTime getLastSubmit() {
        return lastSubmit;
    }
    
    /**
     * 设置最近一次提交时间
     * 
     * @param lastSubmit 最近一次提交时间
     */
    public void setLastSubmit(LocalDateTime lastSubmit) {
        this.lastSubmit = lastSubmit;
    }
    
    @Override
    public String toString() {
        return "UserSubjectProgress{" +
                "id=" + id +
                ", userId=" + userId +
                ", subjectId=" + subjectId +
                ", totalAttempt=" + totalAttempt +
                ", correctCount=" + correctCount +
                ", lastSubmit=" + lastSubmit +
                '}';
    }
}
