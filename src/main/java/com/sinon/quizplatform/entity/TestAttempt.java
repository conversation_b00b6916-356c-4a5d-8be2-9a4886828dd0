package com.sinon.quizplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 用户一次完整测验记录实体类
 * 对应数据库表：test_attempt
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Entity
@Table(name = "test_attempt")
public class TestAttempt {
    
    /**
     * 测验尝试ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 发起用户ID
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 关联的用户实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    /**
     * 所属科目ID
     */
    @Column(name = "subject_id", nullable = false)
    @NotNull(message = "科目ID不能为空")
    private Integer subjectId;
    
    /**
     * 关联的科目实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subject_id", insertable = false, updatable = false)
    private Subject subject;
    
    /**
     * 测验标题（可选）
     */
    @Column(name = "title", length = 255)
    @Size(max = 255, message = "测验标题长度不能超过255位")
    private String title;
    
    /**
     * 题目总数
     */
    @Column(name = "total_questions", nullable = false)
    @NotNull(message = "题目总数不能为空")
    @Min(value = 1, message = "题目总数不能小于1")
    private Integer totalQuestions;
    
    /**
     * 正确题数
     */
    @Column(name = "correct_count", nullable = false)
    @NotNull(message = "正确题数不能为空")
    @Min(value = 0, message = "正确题数不能小于0")
    private Integer correctCount = 0;
    
    /**
     * 总得分（由服务端计算）
     */
    @Column(name = "total_score", nullable = false)
    @NotNull(message = "总得分不能为空")
    @Min(value = 0, message = "总得分不能小于0")
    private Integer totalScore = 0;
    
    /**
     * 开始时间
     */
    @Column(name = "started_at", nullable = false)
    private LocalDateTime startedAt;
    
    /**
     * 结束时间
     */
    @Column(name = "finished_at")
    private LocalDateTime finishedAt;
    
    /**
     * 默认构造函数
     */
    public TestAttempt() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param subjectId 科目ID
     * @param title 测验标题
     * @param totalQuestions 题目总数
     */
    public TestAttempt(Long userId, Integer subjectId, String title, Integer totalQuestions) {
        this.userId = userId;
        this.subjectId = subjectId;
        this.title = title;
        this.totalQuestions = totalQuestions;
        this.correctCount = 0;
        this.totalScore = 0;
        this.startedAt = LocalDateTime.now();
    }
    
    /**
     * JPA生命周期回调：持久化前设置开始时间
     */
    @PrePersist
    protected void onCreate() {
        if (startedAt == null) {
            startedAt = LocalDateTime.now();
        }
    }
    
    /**
     * 完成测验
     */
    public void finish() {
        this.finishedAt = LocalDateTime.now();
    }
    
    /**
     * 判断测验是否已完成
     * 
     * @return 是否已完成
     */
    public boolean isFinished() {
        return finishedAt != null;
    }
    
    /**
     * 计算正确率
     * 
     * @return 正确率（0.0-1.0）
     */
    public double getAccuracyRate() {
        if (totalQuestions == 0) {
            return 0.0;
        }
        return (double) correctCount / totalQuestions;
    }
    
    /**
     * 更新统计信息
     * 
     * @param correctCount 正确题数
     * @param totalScore 总得分
     */
    public void updateStats(Integer correctCount, Integer totalScore) {
        this.correctCount = correctCount;
        this.totalScore = totalScore;
    }
    
    // Getters and Setters
    
    /**
     * 获取测验尝试ID
     * 
     * @return 测验尝试ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 设置测验尝试ID
     * 
     * @param id 测验尝试ID
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public Long getUserId() {
        return userId;
    }
    
    /**
     * 设置用户ID
     * 
     * @param userId 用户ID
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    /**
     * 获取关联的用户实体
     * 
     * @return 用户实体
     */
    public User getUser() {
        return user;
    }
    
    /**
     * 设置关联的用户实体
     * 
     * @param user 用户实体
     */
    public void setUser(User user) {
        this.user = user;
    }
    
    /**
     * 获取科目ID
     * 
     * @return 科目ID
     */
    public Integer getSubjectId() {
        return subjectId;
    }
    
    /**
     * 设置科目ID
     * 
     * @param subjectId 科目ID
     */
    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }
    
    /**
     * 获取关联的科目实体
     * 
     * @return 科目实体
     */
    public Subject getSubject() {
        return subject;
    }
    
    /**
     * 设置关联的科目实体
     * 
     * @param subject 科目实体
     */
    public void setSubject(Subject subject) {
        this.subject = subject;
    }
    
    /**
     * 获取测验标题
     * 
     * @return 测验标题
     */
    public String getTitle() {
        return title;
    }
    
    /**
     * 设置测验标题
     * 
     * @param title 测验标题
     */
    public void setTitle(String title) {
        this.title = title;
    }
    
    /**
     * 获取题目总数
     * 
     * @return 题目总数
     */
    public Integer getTotalQuestions() {
        return totalQuestions;
    }
    
    /**
     * 设置题目总数
     * 
     * @param totalQuestions 题目总数
     */
    public void setTotalQuestions(Integer totalQuestions) {
        this.totalQuestions = totalQuestions;
    }
    
    /**
     * 获取正确题数
     * 
     * @return 正确题数
     */
    public Integer getCorrectCount() {
        return correctCount;
    }
    
    /**
     * 设置正确题数
     * 
     * @param correctCount 正确题数
     */
    public void setCorrectCount(Integer correctCount) {
        this.correctCount = correctCount;
    }
    
    /**
     * 获取总得分
     * 
     * @return 总得分
     */
    public Integer getTotalScore() {
        return totalScore;
    }
    
    /**
     * 设置总得分
     * 
     * @param totalScore 总得分
     */
    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }
    
    /**
     * 获取开始时间
     * 
     * @return 开始时间
     */
    public LocalDateTime getStartedAt() {
        return startedAt;
    }
    
    /**
     * 设置开始时间
     * 
     * @param startedAt 开始时间
     */
    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }
    
    /**
     * 获取结束时间
     * 
     * @return 结束时间
     */
    public LocalDateTime getFinishedAt() {
        return finishedAt;
    }
    
    /**
     * 设置结束时间
     * 
     * @param finishedAt 结束时间
     */
    public void setFinishedAt(LocalDateTime finishedAt) {
        this.finishedAt = finishedAt;
    }
    
    @Override
    public String toString() {
        return "TestAttempt{" +
                "id=" + id +
                ", userId=" + userId +
                ", subjectId=" + subjectId +
                ", title='" + title + '\'' +
                ", totalQuestions=" + totalQuestions +
                ", correctCount=" + correctCount +
                ", totalScore=" + totalScore +
                ", startedAt=" + startedAt +
                ", finishedAt=" + finishedAt +
                '}';
    }
}
