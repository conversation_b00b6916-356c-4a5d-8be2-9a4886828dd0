package com.sinon.quizplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 题目实体类
 * 对应数据库表：question
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Entity
@Table(name = "question")
public class Question {
    
    /**
     * 题目ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 所属科目ID
     */
    @Column(name = "subject_id", nullable = false)
    @NotNull(message = "科目ID不能为空")
    private Integer subjectId;
    
    /**
     * 关联的科目实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subject_id", insertable = false, updatable = false)
    private Subject subject;
    
    /**
     * 题目标题（简述）
     */
    @Column(name = "title", nullable = false, length = 255)
    @NotBlank(message = "题目标题不能为空")
    @Size(max = 255, message = "题目标题长度不能超过255位")
    private String title;
    
    /**
     * 难度等级（0-5）
     */
    @Column(name = "difficulty", nullable = false)
    @NotNull(message = "难度等级不能为空")
    @Min(value = 0, message = "难度等级不能小于0")
    @Max(value = 5, message = "难度等级不能大于5")
    private Integer difficulty;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 最近更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public Question() {
    }
    
    /**
     * 构造函数
     * 
     * @param subjectId 科目ID
     * @param title 题目标题
     * @param difficulty 难度等级
     */
    public Question(Integer subjectId, String title, Integer difficulty) {
        this.subjectId = subjectId;
        this.title = title;
        this.difficulty = difficulty;
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }
    
    /**
     * JPA生命周期回调：持久化前设置创建时间和更新时间
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
    }
    
    /**
     * JPA生命周期回调：更新前设置更新时间
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    
    /**
     * 获取题目ID
     * 
     * @return 题目ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 设置题目ID
     * 
     * @param id 题目ID
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    /**
     * 获取科目ID
     * 
     * @return 科目ID
     */
    public Integer getSubjectId() {
        return subjectId;
    }
    
    /**
     * 设置科目ID
     * 
     * @param subjectId 科目ID
     */
    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }
    
    /**
     * 获取关联的科目实体
     * 
     * @return 科目实体
     */
    public Subject getSubject() {
        return subject;
    }
    
    /**
     * 设置关联的科目实体
     * 
     * @param subject 科目实体
     */
    public void setSubject(Subject subject) {
        this.subject = subject;
    }
    
    /**
     * 获取题目标题
     * 
     * @return 题目标题
     */
    public String getTitle() {
        return title;
    }
    
    /**
     * 设置题目标题
     * 
     * @param title 题目标题
     */
    public void setTitle(String title) {
        this.title = title;
    }
    
    /**
     * 获取难度等级
     * 
     * @return 难度等级
     */
    public Integer getDifficulty() {
        return difficulty;
    }
    
    /**
     * 设置难度等级
     * 
     * @param difficulty 难度等级
     */
    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }
    
    /**
     * 获取创建时间
     * 
     * @return 创建时间
     */
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    /**
     * 设置创建时间
     * 
     * @param createdAt 创建时间
     */
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    /**
     * 获取最近更新时间
     * 
     * @return 最近更新时间
     */
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    /**
     * 设置最近更新时间
     * 
     * @param updatedAt 最近更新时间
     */
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "Question{" +
                "id=" + id +
                ", subjectId=" + subjectId +
                ", title='" + title + '\'' +
                ", difficulty=" + difficulty +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
