package com.sinon.quizplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 选项实体类
 * 对应数据库表：choice_option
 * 一个题目对应多个选项
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Entity
@Table(name = "choice_option")
public class ChoiceOption {
    
    /**
     * 选项ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 所属题目ID
     */
    @Column(name = "question_id", nullable = false)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;
    
    /**
     * 关联的题目实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private Question question;
    
    /**
     * 选项标签（A、B、C、D等）
     */
    @Column(name = "label", nullable = false, length = 1)
    @NotBlank(message = "选项标签不能为空")
    @Size(min = 1, max = 1, message = "选项标签必须为单个字符")
    private String label;
    
    /**
     * 选项内容（Markdown格式）
     */
    @Column(name = "content_md", columnDefinition = "TEXT")
    private String contentMd;
    
    /**
     * 选项图片URL（可选）
     */
    @Column(name = "img_url", length = 255)
    private String imgUrl;
    
    /**
     * 默认构造函数
     */
    public ChoiceOption() {
    }
    
    /**
     * 构造函数
     * 
     * @param questionId 题目ID
     * @param label 选项标签
     * @param contentMd 选项内容
     */
    public ChoiceOption(Long questionId, String label, String contentMd) {
        this.questionId = questionId;
        this.label = label;
        this.contentMd = contentMd;
    }
    
    /**
     * 构造函数（包含图片）
     * 
     * @param questionId 题目ID
     * @param label 选项标签
     * @param contentMd 选项内容
     * @param imgUrl 选项图片URL
     */
    public ChoiceOption(Long questionId, String label, String contentMd, String imgUrl) {
        this.questionId = questionId;
        this.label = label;
        this.contentMd = contentMd;
        this.imgUrl = imgUrl;
    }
    
    // Getters and Setters
    
    /**
     * 获取选项ID
     * 
     * @return 选项ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 设置选项ID
     * 
     * @param id 选项ID
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    /**
     * 获取题目ID
     * 
     * @return 题目ID
     */
    public Long getQuestionId() {
        return questionId;
    }
    
    /**
     * 设置题目ID
     * 
     * @param questionId 题目ID
     */
    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }
    
    /**
     * 获取关联的题目实体
     * 
     * @return 题目实体
     */
    public Question getQuestion() {
        return question;
    }
    
    /**
     * 设置关联的题目实体
     * 
     * @param question 题目实体
     */
    public void setQuestion(Question question) {
        this.question = question;
    }
    
    /**
     * 获取选项标签
     * 
     * @return 选项标签
     */
    public String getLabel() {
        return label;
    }
    
    /**
     * 设置选项标签
     * 
     * @param label 选项标签
     */
    public void setLabel(String label) {
        this.label = label;
    }
    
    /**
     * 获取选项内容
     * 
     * @return 选项内容
     */
    public String getContentMd() {
        return contentMd;
    }
    
    /**
     * 设置选项内容
     * 
     * @param contentMd 选项内容
     */
    public void setContentMd(String contentMd) {
        this.contentMd = contentMd;
    }
    
    /**
     * 获取选项图片URL
     * 
     * @return 选项图片URL
     */
    public String getImgUrl() {
        return imgUrl;
    }
    
    /**
     * 设置选项图片URL
     * 
     * @param imgUrl 选项图片URL
     */
    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
    
    @Override
    public String toString() {
        return "ChoiceOption{" +
                "id=" + id +
                ", questionId=" + questionId +
                ", label='" + label + '\'' +
                ", contentMd='" + contentMd + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                '}';
    }
}
