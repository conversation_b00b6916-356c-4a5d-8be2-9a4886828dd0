package com.sinon.quizplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

/**
 * 选择题扩展字段实体类
 * 对应数据库表：question_choice
 * 与Question表一对一关系
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Entity
@Table(name = "question_choice")
public class QuestionChoice {
    
    /**
     * 对应的题目ID（主键）
     */
    @Id
    @Column(name = "question_id")
    @NotNull(message = "题目ID不能为空")
    private Long questionId;
    
    /**
     * 关联的题目实体（一对一）
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id")
    @MapsId
    private Question question;
    
    /**
     * 题干内容（Markdown格式，可含公式）
     */
    @Column(name = "stem_md", columnDefinition = "LONGTEXT")
    private String stemMd;
    
    /**
     * 题干主图URL（可选）
     */
    @Column(name = "stem_img_url", length = 255)
    private String stemImgUrl;
    
    /**
     * 是否为多选题
     */
    @Column(name = "multi", nullable = false, columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean multi = false;
    
    /**
     * 标准答案JSON数组
     * 例如：["A","C"] 表示正确答案是A和C
     */
    @Column(name = "answer_json", columnDefinition = "JSON")
    private String answerJson;
    
    /**
     * 默认构造函数
     */
    public QuestionChoice() {
    }
    
    /**
     * 构造函数
     * 
     * @param questionId 题目ID
     * @param stemMd 题干内容
     * @param multi 是否多选
     * @param answerJson 标准答案JSON
     */
    public QuestionChoice(Long questionId, String stemMd, Boolean multi, String answerJson) {
        this.questionId = questionId;
        this.stemMd = stemMd;
        this.multi = multi != null ? multi : false;
        this.answerJson = answerJson;
    }
    
    // Getters and Setters
    
    /**
     * 获取题目ID
     * 
     * @return 题目ID
     */
    public Long getQuestionId() {
        return questionId;
    }
    
    /**
     * 设置题目ID
     * 
     * @param questionId 题目ID
     */
    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }
    
    /**
     * 获取关联的题目实体
     * 
     * @return 题目实体
     */
    public Question getQuestion() {
        return question;
    }
    
    /**
     * 设置关联的题目实体
     * 
     * @param question 题目实体
     */
    public void setQuestion(Question question) {
        this.question = question;
    }
    
    /**
     * 获取题干内容
     * 
     * @return 题干内容
     */
    public String getStemMd() {
        return stemMd;
    }
    
    /**
     * 设置题干内容
     * 
     * @param stemMd 题干内容
     */
    public void setStemMd(String stemMd) {
        this.stemMd = stemMd;
    }
    
    /**
     * 获取题干主图URL
     * 
     * @return 题干主图URL
     */
    public String getStemImgUrl() {
        return stemImgUrl;
    }
    
    /**
     * 设置题干主图URL
     * 
     * @param stemImgUrl 题干主图URL
     */
    public void setStemImgUrl(String stemImgUrl) {
        this.stemImgUrl = stemImgUrl;
    }
    
    /**
     * 是否为多选题
     * 
     * @return 是否多选
     */
    public Boolean getMulti() {
        return multi;
    }
    
    /**
     * 设置是否为多选题
     * 
     * @param multi 是否多选
     */
    public void setMulti(Boolean multi) {
        this.multi = multi;
    }
    
    /**
     * 获取标准答案JSON
     * 
     * @return 标准答案JSON
     */
    public String getAnswerJson() {
        return answerJson;
    }
    
    /**
     * 设置标准答案JSON
     * 
     * @param answerJson 标准答案JSON
     */
    public void setAnswerJson(String answerJson) {
        this.answerJson = answerJson;
    }
    
    @Override
    public String toString() {
        return "QuestionChoice{" +
                "questionId=" + questionId +
                ", stemMd='" + stemMd + '\'' +
                ", stemImgUrl='" + stemImgUrl + '\'' +
                ", multi=" + multi +
                ", answerJson='" + answerJson + '\'' +
                '}';
    }
}
