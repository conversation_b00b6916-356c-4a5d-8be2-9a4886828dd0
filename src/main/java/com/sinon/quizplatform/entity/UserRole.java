package com.sinon.quizplatform.entity;

/**
 * 用户角色枚举
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
public enum UserRole {
    /**
     * 管理员
     */
    ADMIN("管理员"),
    
    /**
     * 学生
     */
    STUDENT("学生");
    
    /**
     * 角色描述
     */
    private final String description;
    
    UserRole(String description) {
        this.description = description;
    }
    
    /**
     * 获取角色描述
     * 
     * @return 角色描述
     */
    public String getDescription() {
        return description;
    }
}
