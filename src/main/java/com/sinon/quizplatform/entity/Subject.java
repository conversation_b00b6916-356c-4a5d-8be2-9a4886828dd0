package com.sinon.quizplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 科目实体类
 * 对应数据库表：subject
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Entity
@Table(name = "subject")
public class Subject {
    
    /**
     * 科目ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    /**
     * 科目编码，唯一标识
     * 例如：CET4, CET6, SOFT_SEC
     */
    @Column(name = "code", unique = true, nullable = false, length = 32)
    @NotBlank(message = "科目编码不能为空")
    @Size(max = 32, message = "科目编码长度不能超过32位")
    private String code;
    
    /**
     * 科目名称（可中文）
     */
    @Column(name = "name", nullable = false, length = 64)
    @NotBlank(message = "科目名称不能为空")
    @Size(max = 64, message = "科目名称长度不能超过64位")
    private String name;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 默认构造函数
     */
    public Subject() {
    }
    
    /**
     * 构造函数
     * 
     * @param code 科目编码
     * @param name 科目名称
     */
    public Subject(String code, String name) {
        this.code = code;
        this.name = name;
        this.createdAt = LocalDateTime.now();
    }
    
    /**
     * JPA生命周期回调：持久化前设置创建时间
     */
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }
    
    // Getters and Setters
    
    /**
     * 获取科目ID
     * 
     * @return 科目ID
     */
    public Integer getId() {
        return id;
    }
    
    /**
     * 设置科目ID
     * 
     * @param id 科目ID
     */
    public void setId(Integer id) {
        this.id = id;
    }
    
    /**
     * 获取科目编码
     * 
     * @return 科目编码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 设置科目编码
     * 
     * @param code 科目编码
     */
    public void setCode(String code) {
        this.code = code;
    }
    
    /**
     * 获取科目名称
     * 
     * @return 科目名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 设置科目名称
     * 
     * @param name 科目名称
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * 获取创建时间
     * 
     * @return 创建时间
     */
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    /**
     * 设置创建时间
     * 
     * @param createdAt 创建时间
     */
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "Subject{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
