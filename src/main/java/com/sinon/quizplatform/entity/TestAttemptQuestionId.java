package com.sinon.quizplatform.entity;

import jakarta.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

/**
 * 测验题目明细复合主键类
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Embeddable
public class TestAttemptQuestionId implements Serializable {
    
    /**
     * 所属测验尝试ID
     */
    private Long attemptId;
    
    /**
     * 题目ID
     */
    private Long questionId;
    
    /**
     * 默认构造函数
     */
    public TestAttemptQuestionId() {
    }
    
    /**
     * 构造函数
     * 
     * @param attemptId 测验尝试ID
     * @param questionId 题目ID
     */
    public TestAttemptQuestionId(Long attemptId, Long questionId) {
        this.attemptId = attemptId;
        this.questionId = questionId;
    }
    
    /**
     * 获取测验尝试ID
     * 
     * @return 测验尝试ID
     */
    public Long getAttemptId() {
        return attemptId;
    }
    
    /**
     * 设置测验尝试ID
     * 
     * @param attemptId 测验尝试ID
     */
    public void setAttemptId(Long attemptId) {
        this.attemptId = attemptId;
    }
    
    /**
     * 获取题目ID
     * 
     * @return 题目ID
     */
    public Long getQuestionId() {
        return questionId;
    }
    
    /**
     * 设置题目ID
     * 
     * @param questionId 题目ID
     */
    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TestAttemptQuestionId that = (TestAttemptQuestionId) o;
        return Objects.equals(attemptId, that.attemptId) && 
               Objects.equals(questionId, that.questionId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(attemptId, questionId);
    }
    
    @Override
    public String toString() {
        return "TestAttemptQuestionId{" +
                "attemptId=" + attemptId +
                ", questionId=" + questionId +
                '}';
    }
}
