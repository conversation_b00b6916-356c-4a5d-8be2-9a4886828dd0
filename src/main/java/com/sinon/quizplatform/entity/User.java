package com.sinon.quizplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库表：user
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Entity
@Table(name = "user")
public class User {
    
    /**
     * 用户唯一ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 登录用户名，唯一
     */
    @Column(name = "username", unique = true, nullable = false, length = 64)
    @NotBlank(message = "用户名不能为空")
    @Size(min = 4, max = 20, message = "用户名长度应为4-20位")
    private String username;
    
    /**
     * BCrypt加密后的密码
     */
    @Column(name = "password_hash", nullable = false, length = 60)
    @NotBlank(message = "密码不能为空")
    private String passwordHash;
    
    /**
     * 用户角色
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false)
    @NotNull(message = "用户角色不能为空")
    private UserRole role;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 默认构造函数
     */
    public User() {
    }
    
    /**
     * 构造函数
     * 
     * @param username 用户名
     * @param passwordHash 加密后的密码
     * @param role 用户角色
     */
    public User(String username, String passwordHash, UserRole role) {
        this.username = username;
        this.passwordHash = passwordHash;
        this.role = role;
        this.createdAt = LocalDateTime.now();
    }
    
    /**
     * JPA生命周期回调：持久化前设置创建时间
     */
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }
    
    // Getters and Setters
    
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 设置用户ID
     * 
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    /**
     * 获取用户名
     * 
     * @return 用户名
     */
    public String getUsername() {
        return username;
    }
    
    /**
     * 设置用户名
     * 
     * @param username 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
     * 获取加密后的密码
     * 
     * @return 加密后的密码
     */
    public String getPasswordHash() {
        return passwordHash;
    }
    
    /**
     * 设置加密后的密码
     * 
     * @param passwordHash 加密后的密码
     */
    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }
    
    /**
     * 获取用户角色
     * 
     * @return 用户角色
     */
    public UserRole getRole() {
        return role;
    }
    
    /**
     * 设置用户角色
     * 
     * @param role 用户角色
     */
    public void setRole(UserRole role) {
        this.role = role;
    }
    
    /**
     * 获取创建时间
     * 
     * @return 创建时间
     */
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    /**
     * 设置创建时间
     * 
     * @param createdAt 创建时间
     */
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", role=" + role +
                ", createdAt=" + createdAt +
                '}';
    }
}
