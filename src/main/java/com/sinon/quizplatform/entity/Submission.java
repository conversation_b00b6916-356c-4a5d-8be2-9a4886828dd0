package com.sinon.quizplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * 用户提交记录实体类
 * 对应数据库表：submission
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Entity
@Table(name = "submission")
public class Submission {
    
    /**
     * 提交记录ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 提交者用户ID
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 关联的用户实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    /**
     * 题目ID
     */
    @Column(name = "question_id", nullable = false)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;
    
    /**
     * 关联的题目实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private Question question;
    
    /**
     * 用户选择的答案JSON数组
     * 例如：["A"] 或 ["A","C"]
     */
    @Column(name = "choice_json", columnDefinition = "JSON")
    private String choiceJson;
    
    /**
     * 得分：1=正确，0=错误
     */
    @Column(name = "score", nullable = false)
    @NotNull(message = "得分不能为空")
    @Min(value = 0, message = "得分不能小于0")
    @Max(value = 1, message = "得分不能大于1")
    private Integer score;
    
    /**
     * 提交时间
     */
    @Column(name = "submitted_at", nullable = false)
    private LocalDateTime submittedAt;
    
    /**
     * 默认构造函数
     */
    public Submission() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param questionId 题目ID
     * @param choiceJson 用户选择的答案JSON
     * @param score 得分
     */
    public Submission(Long userId, Long questionId, String choiceJson, Integer score) {
        this.userId = userId;
        this.questionId = questionId;
        this.choiceJson = choiceJson;
        this.score = score;
        this.submittedAt = LocalDateTime.now();
    }
    
    /**
     * JPA生命周期回调：持久化前设置提交时间
     */
    @PrePersist
    protected void onCreate() {
        if (submittedAt == null) {
            submittedAt = LocalDateTime.now();
        }
    }
    
    // Getters and Setters
    
    /**
     * 获取提交记录ID
     * 
     * @return 提交记录ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 设置提交记录ID
     * 
     * @param id 提交记录ID
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public Long getUserId() {
        return userId;
    }
    
    /**
     * 设置用户ID
     * 
     * @param userId 用户ID
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    /**
     * 获取关联的用户实体
     * 
     * @return 用户实体
     */
    public User getUser() {
        return user;
    }
    
    /**
     * 设置关联的用户实体
     * 
     * @param user 用户实体
     */
    public void setUser(User user) {
        this.user = user;
    }
    
    /**
     * 获取题目ID
     * 
     * @return 题目ID
     */
    public Long getQuestionId() {
        return questionId;
    }
    
    /**
     * 设置题目ID
     * 
     * @param questionId 题目ID
     */
    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }
    
    /**
     * 获取关联的题目实体
     * 
     * @return 题目实体
     */
    public Question getQuestion() {
        return question;
    }
    
    /**
     * 设置关联的题目实体
     * 
     * @param question 题目实体
     */
    public void setQuestion(Question question) {
        this.question = question;
    }
    
    /**
     * 获取用户选择的答案JSON
     * 
     * @return 用户选择的答案JSON
     */
    public String getChoiceJson() {
        return choiceJson;
    }
    
    /**
     * 设置用户选择的答案JSON
     * 
     * @param choiceJson 用户选择的答案JSON
     */
    public void setChoiceJson(String choiceJson) {
        this.choiceJson = choiceJson;
    }
    
    /**
     * 获取得分
     * 
     * @return 得分
     */
    public Integer getScore() {
        return score;
    }
    
    /**
     * 设置得分
     * 
     * @param score 得分
     */
    public void setScore(Integer score) {
        this.score = score;
    }
    
    /**
     * 获取提交时间
     * 
     * @return 提交时间
     */
    public LocalDateTime getSubmittedAt() {
        return submittedAt;
    }
    
    /**
     * 设置提交时间
     * 
     * @param submittedAt 提交时间
     */
    public void setSubmittedAt(LocalDateTime submittedAt) {
        this.submittedAt = submittedAt;
    }
    
    @Override
    public String toString() {
        return "Submission{" +
                "id=" + id +
                ", userId=" + userId +
                ", questionId=" + questionId +
                ", choiceJson='" + choiceJson + '\'' +
                ", score=" + score +
                ", submittedAt=" + submittedAt +
                '}';
    }
}
