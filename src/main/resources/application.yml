# Spring Boot 配置文件
spring:
  # 数据源配置
  datasource:
    url: *******************************************************************************************************************************************************
    username: root
    password: Rorochan.123
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update  # 开发阶段使用update，生产环境建议使用validate
    show-sql: true      # 显示SQL语句（开发阶段）
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true  # 格式化SQL输出
        
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:         # 如果Redis设置了密码，在此配置
      database: 0       # 使用的数据库索引
      timeout: 2000ms   # 连接超时时间
      lettuce:
        pool:
          max-active: 8   # 连接池最大连接数
          max-idle: 8     # 连接池最大空闲连接数
          min-idle: 0     # 连接池最小空闲连接数
          max-wait: -1ms  # 连接池最大阻塞等待时间
          
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null  # 忽略null值字段

# JWT配置
jwt:
  secret: quiz-platform-secret-key-for-jwt-token-generation-and-validation-2025
  expiration: 86400    # 访问令牌过期时间（秒），24小时
  refresh-expiration: 604800  # 刷新令牌过期时间（秒），7天

# 日志配置
logging:
  level:
    com.sinon.quizplatform: DEBUG  # 项目日志级别
    org.springframework.security: DEBUG  # Spring Security日志
    org.hibernate.SQL: DEBUG      # Hibernate SQL日志
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE  # SQL参数日志
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 服务器配置
#server:
#  port: 8080
#  servlet:
#    context-path: /api  # API前缀
#    encoding:
#      charset: UTF-8
#      enabled: true
#      force: true
