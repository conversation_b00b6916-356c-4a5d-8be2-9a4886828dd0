---
trigger: model_decision
description: When you are uncertain or unclear about a certain field in the database. When you forget the project's technology stack.
---

基础环境:
Windows11
MYSQL8 用户/密码: root / Rorochan.123

--------------------------------------------------
数据库各表及字段信息如下:

数据库名:	quiz_platform

TABLE: user               # 用户与权限
  id              BIGINT  PK  AUTO_INCREMENT      -- 用户唯一 ID
  username        VARCHAR(64)  UNIQUE             -- 登录名
  password_hash   CHAR(60)                        -- BCrypt 加密后的密码
  role            ENUM('ADMIN','STUDENT')         -- 权限角色
  created_at      DATETIME                        -- 创建时间

TABLE: subject            # 科目/考试分类
  id              INT     PK  AUTO_INCREMENT      -- 科目 ID
  code            VARCHAR(32)  UNIQUE             -- 科目编码：CET4 / CET6 / SOFT_SEC
  name            VARCHAR(64)                     -- 科目名称（可中文）
  created_at      DATETIME                        -- 创建时间

TABLE: question           # 题目公共信息（当前仅选择题）
  id              BIGINT  PK  AUTO_INCREMENT      -- 题目 ID
  subject_id      INT         FK → subject.id     -- 所属科目
  title           VARCHAR(255)                    -- 题目标题（简述）
  difficulty      TINYINT UNSIGNED               -- 难度 0–5
  created_at      DATETIME                        -- 创建时间
  updated_at      DATETIME                        -- 最近更新时间

TABLE: question_choice    # 选择题扩展字段（一对一）
  question_id     BIGINT  PK  FK → question.id    -- 对应题目
  stem_md         LONGTEXT                         -- 题干 Markdown，可含公式
  stem_img_url    VARCHAR(255)  NULL              -- 题干主图 URL（可选）
  multi           BOOLEAN DEFAULT FALSE           -- 是否多选
  answer_json     JSON                            -- 标准答案数组，如 ["A","C"]

TABLE: choice_option      # 选项表（一题多行）
  id              BIGINT  PK  AUTO_INCREMENT      -- 选项 ID
  question_id     BIGINT      FK → question.id    -- 所属题目
  label           CHAR(1)                          -- 选项标签，如 A/B/C
  content_md      TEXT                             -- 选项内容 Markdown
  img_url         VARCHAR(255)  NULL              -- 选项图片 URL（可选）

TABLE: submission         # 用户提交记录
  id              BIGINT  PK  AUTO_INCREMENT      -- 提交记录 ID
  user_id         BIGINT      FK → user.id        -- 提交者
  question_id     BIGINT      FK → question.id    -- 题目
  choice_json     JSON                             -- 用户选择的答案数组
  score           TINYINT UNSIGNED                -- 得分：1=正确 0=错误
  submitted_at    DATETIME                         -- 提交时间

TABLE: user_subject_progress   # 用户在各科目的统计（可选表）
  id              BIGINT  PK  AUTO_INCREMENT      -- 记录 ID
  user_id         BIGINT      FK → user.id        -- 用户
  subject_id      INT         FK → subject.id     -- 科目
  total_attempt   INT                              -- 累计作答次数
  correct_count   INT                              -- 累计正确次数
  last_submit     DATETIME                         -- 最近一次提交时间

TABLE: test_attempt             # 用户一次完整测验记录
  id               BIGINT PK  AUTO_INCREMENT      -- 测验尝试 ID
  user_id          BIGINT     FK → user.id        -- 发起用户
  subject_id       INT        FK → subject.id     -- 所属科目
  title            VARCHAR(255) NULL              -- 测验标题（可选）
  total_questions  INT                             -- 题目总数
  correct_count    INT                             -- 正确题数
  total_score      INT                             -- 总得分（由服务端计算）
  started_at       DATETIME                        -- 开始时间
  finished_at      DATETIME                        -- 结束时间

TABLE: test_attempt_question    # 测验中的题目明细
  attempt_id       BIGINT     FK → test_attempt.id -- 所属测验尝试
  question_id      BIGINT     FK → question.id     -- 题目 ID
  order_no         INT                              -- 在测验中的顺序
  choice_json      JSON                             -- 用户选择答案数组
  score            TINYINT UNSIGNED                 -- 得分：1=正确 0=错误
  PRIMARY KEY (attempt_id, question_id)             -- 复合主键，确保唯一
--------------------------------------------------
基础技术栈(可扩充)

后端：Java 17 + Spring Boot 3.5 + Maven3
  · 模块：Spring Web + Spring Security
  · 鉴权：JWT

数据库：MySQL 8  

缓存：Redis 7  
  · JWT

前端框架：Vue 3 + Vite  
状态管理：Pinia  
  · loginStore 持 JWT，持久化到 localStorage  
  · Axios 拦截器写 Authorization: Bearer <token>  
  · 路由守卫：未登录跳 /login

UI 组件库：Naive UI（按需引入）  
自研组件包：ui-question  
  · Choice 题型  
  · Markdown + 图片 + KaTeX 公式渲染

判题子系统：仅比对答案，本地实现；后期可拆服务

预期场景：  
1. 单机课设演示；做题前必须登录。  
2. 题干/选项支持文本、图片、公式。  
3. 支持按科目（如英语4级、英语6级、软考）筛题，数据库已留 subject 表以便扩展。  
4. 不考虑上线与大规模并发。
--------------------------------------------------